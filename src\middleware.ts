// src/middleware.ts
import { NextResponse } from "next/server";
import type { NextRequest } from 'next/server';

// Korumasız rotalar
const publicRoutes = [
  "/",
  "/sign-in",
  "/sign-up",
  "/api",
  "/teachers",
  /^\/teachers\/[^/]+$/,
  "/hakkimizda",
  "/iletisim",
  "/blog",
  /^\/blog\/.*$/,
  "/sss",
  "/unauthorized",
  "/ogretmenler",
  "/kurslar",
  "/seviye-testi",
  "/search-results",
  "/yardim",
  "/gizlilik-politikasi",
  "/kullanim-kosullari",
  "/kvkk-aydinlatma-metni",
  "/uyelik-sozlesmesi",
  // Statik dosyalar
  "/favicon.ico",
  "/robots.txt",
  "/sitemap.xml",
  "/logo.png",
  /^\/.*\.(svg|png|jpg|jpeg|gif|webp|ico)$/,
  // Next.js dahili rotalar
  /^\/_next\/.*/,
  /^\/api\/.*/,
];

// Öğretmen rotaları (Admin de erişebilir)
const teacherRoutes = [
  "/(dashboard)/teacher",
];

// Öğrenci rotaları (Admin de erişebilir)
const studentRoutes = [
  "/(dashboard)/student",
];

// Veli rotaları (Admin de erişebilir)
const parentRoutes = [
  "/(dashboard)/parent",
];

// Admin rotaları (Sadece admin erişebilir)
const adminRoutes = [
  "/(dashboard)/admin",
];

import { Role } from '@/types/user.types'; // Role enum'ını import et
import { createSupabaseServerClient } from '@/utils/supabase/server';

// Kullanıcı rolü tipleri
// type UserRole = "admin" | "teacher" | "student" | "parent" | "user" | null | undefined; // Role enum ile değiştirildi

const routeMatches = (path: string, routes: (string | RegExp)[]): boolean => {
  return routes.some(route => {
    if (route instanceof RegExp) {
      return route.test(path);
    }
    const dashboardPath = path.startsWith('/(dashboard)') ? path.substring('/(dashboard)'.length) : path;
    const routePath = route.startsWith('/(dashboard)') ? route.substring('/(dashboard)'.length) : route;
    // Ensure matching starts from the beginning of the path segment
    return dashboardPath.startsWith(routePath) && (dashboardPath.length === routePath.length || dashboardPath[routePath.length] === '/');
  });
};

export default async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;

  // GELİŞTİRME ORTAMI İÇİN GEÇİCİ AUTH ATLAMA
  if (process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
    console.warn(`MIDDLEWARE_DEBUG: DEV_SKIP_AUTH_MIDDLEWARE aktif, TÜM KİMLİK DOĞRULAMA VE ROL KONTROLLERİ ATLANDI! Path: ${path}`);
    if (process.env.NODE_ENV === 'production') {
      console.error("CRITICAL SECURITY WARNING: DEV_SKIP_AUTH_MIDDLEWARE is active in a production environment! This should NEVER happen.");
    }
    return NextResponse.next();
  }

  // TODO: Seçilen kimlik doğrulama çözümünden kullanıcı oturum bilgilerini al.
  // Örnek (NextAuth.js için): import { getToken } from 'next-auth/jwt'; const token = await getToken({ req }); const userId = token?.sub; const userRole = token?.role as Role;
  // Örnek (Supabase için): const { data: { session } } = await supabase.auth.getSession(); const userId = session?.user?.id; const userRole = session?.user?.user_metadata?.role as Role;

  const supabase = createSupabaseServerClient(); // next/headers cookies() kullanır
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();

  let userId: string | null | undefined = null;
  let effectiveRole: Role | null | undefined = null; // Tipini Role olarak güncelle

  if (session && !sessionError) {
    userId = session.user.id;
    // user_metadata.role yoksa veya tanımsızsa varsayılan bir rol ata (örn: Role.USER)
    effectiveRole = (session.user.user_metadata?.role as Role) || Role.USER;
  } else if (sessionError) {
    console.error("Middleware Supabase session error:", sessionError.message);
    // Session hatası durumunda ne yapılacağına karar verilmeli,
    // belki public olmayan sayfalara erişimi engellemek iyi bir başlangıç.
    // Şimdilik userId ve effectiveRole null kalacak, bu da public olmayan sayfalarda login'e yönlendirecektir.
  }

  // console.log(`MIDDLEWARE_DEBUG: Path: ${path}, UserID: ${userId}, Role: ${effectiveRole}`);


  // Public rotaları kontrol et
  if (publicRoutes.some(route => route instanceof RegExp ? route.test(path) : path === route)) {
    // console.log(`MIDDLEWARE_DEBUG: Path ${path} is public.`);
    return NextResponse.next();
  }

  // Kullanıcı giriş yapmamışsa ve rota korumalıysa, giriş sayfasına yönlendir
  if (!userId) {
    const signInUrl = new URL('/sign-in', req.url);
    signInUrl.searchParams.set('redirect_url', req.nextUrl.pathname);
    console.log(`MIDDLEWARE_INFO: Kullanıcı giriş yapmamış, ${signInUrl.toString()}'a yönlendiriliyor. İstenen yol: ${path}`);
    return NextResponse.redirect(signInUrl);
  }

  // Kullanıcı giriş yapmışsa, rol kontrolü yap
  if (!effectiveRole || !Object.values(Role).includes(effectiveRole)) {
    console.warn(`MIDDLEWARE_WARN: Geçersiz veya eksik rol (EffectiveRole: ${effectiveRole}) userId: ${userId}. /unauthorized'a yönlendiriliyor. Path: ${path}`);
    return NextResponse.redirect(new URL('/unauthorized?error=invalid_role', req.url));
  }

  // Admin rotalarına erişim kontrolü
  if (routeMatches(path, adminRoutes) && effectiveRole !== Role.ADMIN) {
    console.warn(`MIDDLEWARE_WARN: Yetkisiz erişim denemesi (Admin): EffectiveRole=${effectiveRole}, UserID=${userId}, Path=${path}`);
    return NextResponse.redirect(new URL('/unauthorized?error=admin_required', req.url));
  }

  // Öğretmen rotalarına erişim kontrolü (Adminler de öğretmen rotalarına erişebilir)
  if (routeMatches(path, teacherRoutes) && effectiveRole !== Role.TEACHER && effectiveRole !== Role.ADMIN) {
    console.warn(`MIDDLEWARE_WARN: Yetkisiz erişim denemesi (Teacher): EffectiveRole=${effectiveRole}, UserID=${userId}, Path=${path}`);
    return NextResponse.redirect(new URL('/unauthorized?error=teacher_required', req.url));
  }

  // Öğrenci rotalarına erişim kontrolü (Adminler de öğrenci rotalarına erişebilir)
  if (routeMatches(path, studentRoutes) && effectiveRole !== Role.STUDENT && effectiveRole !== Role.ADMIN) {
    console.warn(`MIDDLEWARE_WARN: Yetkisiz erişim denemesi (Student): EffectiveRole=${effectiveRole}, UserID=${userId}, Path=${path}`);
    return NextResponse.redirect(new URL('/unauthorized?error=student_required', req.url));
  }

  // Veli rotalarına erişim kontrolü (Adminler de veli rotalarına erişebilir)
  if (routeMatches(path, parentRoutes) && effectiveRole !== Role.PARENT && effectiveRole !== Role.ADMIN) {
    console.warn(`MIDDLEWARE_WARN: Yetkisiz erişim denemesi (Parent): EffectiveRole=${effectiveRole}, UserID=${userId}, Path=${path}`);
    return NextResponse.redirect(new URL('/unauthorized?error=parent_required', req.url));
  }

  // console.log(`MIDDLEWARE_DEBUG: Access granted for Path: ${path}, UserID: ${userId}, Role: ${effectiveRole}`);
  return NextResponse.next();
}

export const config = {
  matcher: [
    // Statik dosyaları, API routes'ları ve development dosyalarını hariç tut
    "/((?!api/|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|logo.png|.*\\.svg|.*\\.png|.*\\.jpg|.*\\.jpeg|.*\\.gif|.*\\.webp|.*\\.ico).*)",
    // Sadece korumalı rotaları dahil et
    "/(dashboard)/:path*",
  ],
};
