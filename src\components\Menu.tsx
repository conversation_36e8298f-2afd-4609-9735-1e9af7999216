// src/components/Menu.tsx
"use client";

// src/components/Menu.tsx
"use client";

// src/components/Menu.tsx
"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut } from "@/lib/actions/auth.actions";
// Removed duplicate import of useTransition
import {
  LayoutDashboard,
  Users,
  GraduationCap,
  Settings,
  CalendarCheck,
  MessageSquare,
  Wallet,
  LogOut,
  UserCheck,
  Search,
  Home,
  School,
  BookOpen,
  Calendar,
  User,
  HelpCircle,
  BarChart3,
  Briefcase,
  PieChart
} from "lucide-react";
import cn from "classnames";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useState, useEffect, useTransition } from "react"; // Using standard useTransition

// Menü öğeleri - rol bazlı erişim ile
const menuItems = [
  // Ana <PERSON>ri
  { href: "/dashboard", label: "Kontrol Paneli", icon: LayoutDashboard, visible: ["admin", "teacher", "student", "parent"] },
  { href: "/", label: "Ana Sayfa", icon: Home, visible: ["admin", "teacher", "student", "parent"] },
  
  // Admin için öğeler
  { href: "/admin", label: "Yönetim", icon: Briefcase, visible: ["admin"] },
  { href: "/admin/teachers", label: "Öğretmenler", icon: School, visible: ["admin"] },
  { href: "/admin/teacher-applications", label: "Öğretmen Başvuruları", icon: UserCheck, visible: ["admin"] },
  { href: "/admin/students", label: "Öğrenciler", icon: GraduationCap, visible: ["admin"] },
  { href: "/admin/parents", label: "Veliler", icon: Users, visible: ["admin"] },
  { href: "/admin/bookings", label: "Rezervasyonlar", icon: CalendarCheck, visible: ["admin"] },
  { href: "/admin/reports", label: "Raporlar", icon: BarChart3, visible: ["admin"] },
  { href: "/admin/finance", label: "Finans", icon: PieChart, visible: ["admin"] },
  
  // Öğretmen için öğeler
  { href: "/teacher", label: "Öğretmen", icon: School, visible: ["teacher"] },
  { href: "/teacher/schedule", label: "Ders Takvimi", icon: Calendar, visible: ["teacher"] },
  { href: "/teacher/my-students", label: "Öğrencilerim", icon: Users, visible: ["teacher"] },
  { href: "/teacher/my-lessons", label: "Derslerim", icon: BookOpen, visible: ["teacher"] },
  { href: "/teacher/earnings", label: "Kazançlarım", icon: Wallet, visible: ["teacher"] },
  
  // Öğrenci için öğeler
  { href: "/student", label: "Öğrenci", icon: GraduationCap, visible: ["student"] },
  { href: "/student/my-lessons", label: "Derslerim", icon: BookOpen, visible: ["student"] },
  { href: "/ogretmenler", label: "Öğretmen Bul", icon: Search, visible: ["student", "parent"] },
  
  // Veli için öğeler
  { href: "/parent", label: "Veli", icon: Users, visible: ["parent"] },
  { href: "/parent/my-children", label: "Çocuklarım", icon: Users, visible: ["parent"] },
  { href: "/parent/children-bookings", label: "Çocuk Dersleri", icon: CalendarCheck, visible: ["parent"] },
  
  // Herkes için ortak öğeler
  { href: "/messages", label: "Mesajlar", icon: MessageSquare, visible: ["admin", "teacher", "student", "parent"] },
  { href: "/profile", label: "Profilim", icon: User, visible: ["admin", "teacher", "student", "parent"] },
  { href: "/settings", label: "Ayarlar", icon: Settings, visible: ["admin", "teacher", "student", "parent"] },
  { href: "/help", label: "Yardım", icon: HelpCircle, visible: ["admin", "teacher", "student", "parent"] },
];

type Role = string | null | undefined;

export default function Menu() {
  const pathname = usePathname();
  const [isMounted, setIsMounted] = useState(false);
  const [isHovered, setIsHovered] = useState<string | null>(null);
  const [isPendingSignOut, startSignOutTransition] = useTransition(); // Standard useTransition

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const userRole: Role = "admin"; // Placeholder, to be replaced with Supabase logic

  const handleSignOut = () => {
    startSignOutTransition(async () => {
      await signOut("/"); // Redirect to home page after sign out
    });
  };

  // Kullanıcının rolüne göre menü öğelerini filtrele ve kategorize et
  const filteredItems = menuItems.filter(item => item.visible.includes(userRole as string));
  
  // Kategorilere ayır
  const mainItems = filteredItems.filter(item => ['/dashboard', '/'].includes(item.href));
  const roleSpecificItems = filteredItems.filter(item => 
    !(['/dashboard', '/', '/messages', '/profile', '/settings', '/help'].includes(item.href))
  );
  const commonItems = filteredItems.filter(item => 
    ['/messages', '/profile', '/settings', '/help'].includes(item.href)
  );

  if (!isMounted) {
    return (
      <div className="animate-pulse flex flex-col gap-4 p-2">
        {[1, 2, 3, 4, 5].map(i => (
          <div key={i} className="h-10 bg-gray-200 dark:bg-gray-700 rounded-md w-full mx-2"></div>
        ))}
      </div>
    );
  }

  const MenuItem = ({ item }: { item: typeof menuItems[0] }) => {
    // Aktif menü öğesini daha kesin belirle
    let isActive = false;
    
    // Ana sayfa için özel durum
    if (item.href === "/") {
      isActive = pathname === "/";
    }
    // Dashboard için özel durum
    else if (item.href === "/dashboard") {
      isActive = pathname === "/dashboard";
    }
    // Ana kategori menüleri için (admin, teacher, student, parent)
    else if (['/admin', '/teacher', '/student', '/parent'].includes(item.href)) {
      // Eğer tam olarak bu sayfa ise veya alt sayfaları ise
      isActive = pathname === item.href || (
        pathname.startsWith(item.href + "/") && 
        // Alt menü öğeleri için kontrol
        !menuItems.some(subItem => 
          subItem.href !== item.href && 
          subItem.href.startsWith(item.href + "/") && 
          pathname.startsWith(subItem.href)
        )
      );
    }
    // Alt kategori menüleri için (admin/teachers, teacher/schedule vb.)
    else if (item.href.includes("/")) {
      isActive = pathname === item.href || pathname.startsWith(item.href + "/");
    }
    // Diğer tüm menü öğeleri için tam eşleşme
    else {
      isActive = pathname === item.href;
    }
    const Icon = item.icon;
    
    return (
      (<TooltipProvider delayDuration={0}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Link
              href={item.href}
              className={cn(
                "group flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-all duration-200 relative",
                isActive 
                  ? "bg-primary/15 text-primary hover:bg-primary/20" 
                  : "text-muted-foreground hover:bg-accent hover:text-foreground"
              )}
              onMouseEnter={() => setIsHovered(item.href)}
              onMouseLeave={() => setIsHovered(null)}>
              {isActive && (
                <div className="absolute left-0 inset-y-0 w-1 rounded-r-full bg-primary transition-all duration-200" />
              )}
              <Icon className={cn(
                "h-4 w-4 flex-shrink-0 transition-transform duration-200",
                isActive ? "text-primary" : "text-muted-foreground group-hover:text-foreground",
                isHovered === item.href && "scale-110"
              )} />
              <span className="ml-2 truncate">{item.label}</span>
            </Link>
          </TooltipTrigger>
          <TooltipContent side="right" className="text-xs">
            {item.label}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>)
    );
  };

  return (
    <nav className="h-full flex flex-col pt-0 pb-1 bg-card text-card-foreground">
      <div className="flex-1 w-full flex flex-col space-y-0.5 overflow-y-auto custom-scrollbar">
        {/* Ana Menü Öğeleri */}
        <div className="px-1 py-1">
          {mainItems.map((item) => (
            <MenuItem key={item.href} item={item} />
          ))}
        </div>

        {/* Role Özel Öğeler */}
        {roleSpecificItems.length > 0 && (
          <div className="px-1 py-1 border-t border-border/10">
            {roleSpecificItems.map((item) => (
              <MenuItem key={item.href} item={item} />
            ))}
          </div>
        )}

        {/* Ortak Öğeler */}
        <div className="mt-auto px-1 py-1 border-t border-border/10">
          {commonItems.map((item) => (
            <MenuItem key={item.href} item={item} />
          ))}
        </div>
      </div>
      
      {/* Çıkış Yap Butonu */}
      <div className="mt-auto pt-4 border-t border-border/20 w-full px-3">
        <Button
          variant="ghost"
          className={cn(
            "w-full flex items-center justify-start transition-all duration-150",
            "text-red-500 hover:text-red-600 hover:bg-red-100/30 dark:hover:bg-red-900/20",
            "px-3 py-2 rounded-md"
          )}
          size="sm"
          onClick={handleSignOut}
          disabled={isPendingSignOut}
        >
          <LogOut className="h-5 w-5 flex-shrink-0" />
          <span className="ml-3 font-medium">
            {isPendingSignOut ? "Çıkılıyor..." : "Çıkış Yap"}
          </span>
        </Button>
      </div>
    </nav>
  );
}
